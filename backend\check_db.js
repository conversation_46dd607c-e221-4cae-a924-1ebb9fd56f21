const { Pool } = require('pg');

async function checkDatabase() {
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL
  });
  
  try {
    console.log('🔍 Checking database for recent sessions...');
    
    // Check total sessions for user 1
    const countResult = await pool.query('SELECT COUNT(*) FROM prototype_sessions WHERE user_id = 1');
    console.log(`📊 Total sessions for user 1: ${countResult.rows[0].count}`);
    
    // Get recent sessions
    const recentQuery = `
      SELECT 
        id,
        prototype_id,
        page_url,
        LENGTH(page_html) as html_length,
        session_state,
        created_at,
        updated_at
      FROM prototype_sessions 
      WHERE user_id = 1 
      ORDER BY updated_at DESC 
      LIMIT 5
    `;
    
    const recentResult = await pool.query(recentQuery);
    
    if (recentResult.rows.length === 0) {
      console.log('❌ No sessions found for user 1');
    } else {
      console.log(`✅ Found ${recentResult.rows.length} recent sessions:`);
      
      for (let i = 0; i < recentResult.rows.length; i++) {
        const row = recentResult.rows[i];
        console.log(`\n📄 Session ${i + 1}:`);
        console.log(`   ID: ${row.id}`);
        console.log(`   Prototype ID: ${row.prototype_id}`);
        console.log(`   Page URL: ${row.page_url}`);
        console.log(`   HTML Length: ${row.html_length} chars`);
        console.log(`   State: ${row.session_state}`);
        console.log(`   Created: ${row.created_at}`);
        console.log(`   Updated: ${row.updated_at}`);
        
        // Check the actual HTML content for our test changes
        const htmlQuery = 'SELECT page_html FROM prototype_sessions WHERE id = $1';
        const htmlResult = await pool.query(htmlQuery, [row.id]);
        
        if (htmlResult.rows.length > 0) {
          const html = htmlResult.rows[0].page_html;
          const preview = html.substring(0, 300);
          console.log(`   HTML Preview: ${preview}...`);
          
          // Check for our test changes
          if (html.includes('Submit') && html.includes('button')) {
            console.log('   🎯 CONTAINS "Submit" BUTTON CHANGE!');
          }
          if (html.includes('Dont remeb') || html.includes("Don't remember")) {
            console.log('   🎯 CONTAINS "Don\'t remember password" CHANGE!');
          }
          if (html.includes('Forgot your password')) {
            console.log('   📝 Still contains original "Forgot your password"');
          }
        }
      }
    }
    
  } catch (error) {
    console.error('❌ Database error:', error);
  } finally {
    await pool.end();
  }
}

checkDatabase();
