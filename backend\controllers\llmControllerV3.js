const llmServiceV3 = require('../services/llmServiceV3');
const versionService = require('../services/versionService');

/**
 * V3 LLM Controller - Clean implementation based on Readdy.ai approach
 * Focuses on accurate, targeted editing through sophisticated prompting
 * Enhanced with prototyping robustness and validation
 */



// Removed duplicate generateIntent function - see line 188 for the actual implementation

/**
 * Implement feature functionality (inline/modal/page) with server-side prompts
 */
async function implementFeature(req, res, next) {
  try {
    if (!req.session) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { htmlContent, elementText, elementType, implementationType, conversationHistory, intentData } = req.body;
    if (!htmlContent || !elementText || !implementationType) {
      return res.status(400).json({
        error: 'HTML content, element text, and implementation type are required'
      });
    }

    // Set SSE headers
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    });

    // Get user information for version tracking
    const userId = req.user?.dbId || req.user?.id;

    await llmServiceV3.implementFeature(
      htmlContent,
      elementText,
      elementType,
      implementationType,
      res,
      conversationHistory || [],
      intentData,
      { projectId: req.body.projectId, userId }
    );
  } catch (error) {
    console.error('Error in implementFeature:', error);
    next(error);
  }
}

/**
 * Generate complete HTML from prompt
 */
async function generateHTML(req, res, next) {
  try {
    if (!req.session) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { prompt, projectId, pageTitle } = req.body;
    if (!prompt) {
      return res.status(400).json({ error: 'Prompt is required' });
    }

    // Get user information for session creation
    const userId = req.user?.dbId || req.user?.id;
    if (!userId) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    console.log('🎯 [Controller] generateHTML received request');
    console.log('📏 Prompt length:', prompt.length);
    console.log('🏗️ Project ID:', projectId);
    console.log('👤 User ID:', userId);
    console.log('📄 Page Title:', pageTitle);
    console.log('🔍 Contains plan data:', prompt.includes('DETAILED IMPLEMENTATION PLAN'));
    console.log('🔍 Contains sections:', prompt.includes('Implementation Requirements'));
    console.log('🔍 Contains features:', prompt.includes('Key Features'));

    // Set SSE headers
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    });

    await llmServiceV3.generateHTML(prompt, res, null, {
      projectId,
      userId,
      pageTitle
    });
  } catch (error) {
    console.error('Error in generateHTML:', error);
    next(error);
  }
}

/**
 * Edit existing HTML with targeted changes (Readdy.ai style)
 * Enhanced with prototyping context for demo-ready functionality
 */
async function editHTML(req, res, next) {
  try {
    if (!req.session) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { htmlContent, prompt, elementSelector, conversationHistory, fragmentHtml, implementationType } = req.body;

    console.log('🎯 [Controller] editHTML received request');
    console.log('📏 HTML content length:', htmlContent?.length || 0);
    console.log('📏 Fragment HTML length:', fragmentHtml?.length || 0);
    console.log('📏 Prompt length:', prompt?.length || 0);
    console.log('🔧 Element selector:', elementSelector);
    console.log('🔧 Implementation type:', implementationType);
    console.log('📝 Conversation history length:', conversationHistory?.length || 0);

    if ((!htmlContent && !fragmentHtml) || !prompt) {
      return res.status(400).json({
        error: 'HTML content or fragmentHtml and prompt are required'
      });
    }

    // Add prototyping context to enhance prompt robustness
    const prototypingContext = `
PROTOTYPING CONTEXT: This is for live client demonstration
DEMO REQUIREMENTS: All functionality must work immediately
QUALITY STANDARD: Professional, polished, stakeholder-ready
ERROR PREVENTION: Include complete JavaScript functions with error handling
`;

    // Set SSE headers
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    });

    // Enhanced prompt with prototyping requirements
    const enhancedPrompt = prototypingContext + prompt;

    // Get user information for version tracking
    const userId = req.user?.dbId || req.user?.id;

    await llmServiceV3.editHTML(
      htmlContent,
      enhancedPrompt,
      res,
      null,
      elementSelector,
      conversationHistory || [],
      { projectId: req.body.projectId, userId, fragmentHtml }
    );
  } catch (error) {
    console.error('Error in editHTML:', error);
    next(error);
  }
}

/**
 * Step 1: Generate Intent from element click (like Readdy's /api/page_gen/generate_intent)
 */
async function generateIntent(req, res, next) {
  try {
    if (!req.session) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { elementCode, htmlContent, conversationHistory } = req.body;
    if (!elementCode || !htmlContent) {
      return res.status(400).json({
        error: 'Element code and HTML content are required'
      });
    }

    const result = await llmServiceV3.generateIntent(
      elementCode,
      htmlContent,
      conversationHistory || []
    );

    if (result.success) {
      res.json({
        code: 'OK',
        data: result.intent,
        meta: {
          time: Date.now(),
          request_id: `intent_${Date.now()}`,
          message: '',
          detail: null
        }
      });
    } else {
      res.status(500).json({
        code: 'ERROR',
        error: result.error,
        meta: {
          time: Date.now(),
          request_id: `intent_${Date.now()}`,
          message: 'Failed to generate intent',
          detail: result.error
        }
      });
    }
  } catch (error) {
    console.error('Error in generateIntent:', error);
    next(error);
  }
}

/**
 * Generate structured plan from prompt (for plan review page)
 */
async function generatePlan(req, res, next) {
  try {
    if (!req.session) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { prompt, deviceType } = req.body;
    if (!prompt) {
      return res.status(400).json({ error: 'Prompt is required' });
    }

    const result = await llmServiceV3.generateStructuredPlan(prompt, deviceType);

    if (result.success) {
      res.json({ plan: result.plan });
    } else {
      res.status(500).json({ error: result.error });
    }
  } catch (error) {
    console.error('Error in generatePlan:', error);
    next(error);
  }
}

/**
 * Generate streaming plan (for chat)
 */
async function generateStreamingPlan(req, res, next) {
  try {
    if (!req.session) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { prompt } = req.body;
    if (!prompt) {
      return res.status(400).json({ error: 'Prompt is required' });
    }

    // Set SSE headers
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    });

    await llmServiceV3.generatePlan(prompt, res);
  } catch (error) {
    console.error('Error in generateStreamingPlan:', error);
    next(error);
  }
}

/**
 * Generate code from plan
 */
async function generateCode(req, res, next) {
  try {
    // Throw not implemented error
    return res.status(501).json({
      error: 'Not implemented',
      message: 'generateCode functionality is not yet implemented'
    });
  } catch (error) {
    console.error('Error in generateCode:', error);
    next(error);
  }
}

module.exports = {
  generateIntent,
  implementFeature,
  generateHTML,
  editHTML,
  generatePlan,
  generateStreamingPlan,
  generateCode
};
