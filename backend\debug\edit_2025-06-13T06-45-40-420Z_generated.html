<div id="app">
  <nav class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between h-16">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <h1 class="text-xl font-semibold text-gray-900 mr-8">CRM Dashboard</h1>
          </div>
          <div class="flex space-x-2">
            <button data-nav="dashboard" class="border-blue-500 text-gray-900 whitespace-nowrap py-2 px-4 mx-1 border-b-2 font-medium text-sm">Dashboard</button>
            <button data-nav="contacts" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-4 mx-1 border-b-2 font-medium text-sm transition-colors duration-200 rounded-t-lg hover:bg-gray-50">Contacts</button>
            <button data-nav="opportunities" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-4 mx-1 border-b-2 font-medium text-sm transition-colors duration-200 rounded-t-lg hover:bg-gray-50">Opportunities</button>
            <button data-nav="reports" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-4 mx-1 border-b-2 font-medium text-sm transition-colors duration-200 rounded-t-lg hover:bg-gray-50">Reports</button>
          </div>
        </div>
        <div class="flex items-center">
          <div class="text-sm text-gray-500 mr-4">
            <i class="fas fa-user-circle mr-1"></i>
            John Smith
          </div>
        </div>
      </div>
    </div>
  </nav>

  <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <section data-view="dashboard">
      <div class="flex justify-between items-center mb-8">
        <h2 class="text-2xl font-bold text-gray-900">Dashboard Overview</h2>
        <div class="flex space-x-4">
          <button data-action="openModal" data-target="newOpportunityModal" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200">
            <i class="fas fa-plus mr-2"></i> New Opportunity
          </button>
          <button data-action="openModal" data-target="quickContactModal" class="bg-white hover:bg-gray-50 text-gray-700 border border-gray-300 px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200">
            <i class="fas fa-user-plus mr-2"></i> Add Contact
          </button>
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-500">Total Opportunities</p>
              <p class="text-3xl font-bold text-gray-900 mt-1">24</p>
            </div>
            <div class="bg-blue-100 p-3 rounded-full">
              <i class="fas fa-chart-line text-blue-600"></i>
            </div>
          </div>
          <p class="text-sm text-green-600 mt-2"><span class="font-medium">+12%</span> from last month</p>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-500">Active Contacts</p>
              <p class="text-3xl font-bold text-gray-900 mt-1">156</p>
            </div>
            <div class="bg-green-100 p-3 rounded-full">
              <i class="fas fa-users text-green-600"></i>
            </div>
          </div>
          <p class="text-sm text-green-600 mt-2"><span class="font-medium">+8%</span> from last month</p>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-500">Closed Won</p>
              <p class="text-3xl font-bold text-gray-900 mt-1">$124K</p>
            </div>
            <div class="bg-purple-100 p-3 rounded-full">
              <i class="fas fa-trophy text-purple-600"></i>
            </div>
          </div>
          <p class="text-sm text-green-600 mt-2"><span class="font-medium">+22%</span> from last month</p>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-500">Pipeline Value</p>
              <p class="text-3xl font-bold text-gray-900 mt-1">$342K</p>
            </div>
            <div class="bg-yellow-100 p-3 rounded-full">
              <i class="fas fa-funnel-dollar text-yellow-600"></i>
            </div>
          </div>
          <p class="text-sm text-red-600 mt-2"><span class="font-medium">-5%</span> from last month</p>
        </div>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow p-6 lg:col-span-2">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900">Opportunities by Stage</h3>
            <button data-action="openModal" data-target="fullReportModal" class="text-sm text-blue-600 hover:text-blue-800 font-medium">View Full Report</button>
          </div>
          <div class="h-64" data-action="renderChart" data-chart-type="bar" data-chart-id="opportunitiesChart"></div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900">Top Opportunities</h3>
            <button data-action="openModal" data-target="allOpportunitiesModal" class="text-sm text-blue-600 hover:text-blue-800 font-medium">View All</button>
          </div>
          <div class="space-y-4">
            <div class="flex items-start">
              <div class="bg-blue-100 p-2 rounded-full mr-3">
                <i class="fas fa-briefcase text-blue-600 text-sm"></i>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-900">Acme Corp - Cloud Migration</p>
                <p class="text-xs text-gray-500">$45,000 • 80% probability</p>
              </div>
            </div>
            <div class="flex items-start">
              <div class="bg-green-100 p-2 rounded-full mr-3">
                <i class="fas fa-briefcase text-green-600 text-sm"></i>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-900">XYZ Inc - Annual Contract</p>
                <p class="text-xs text-gray-500">$32,000 • 65% probability</p>
              </div>
            </div>
            <div class="flex items-start">
              <div class="bg-purple-100 p-2 rounded-full mr-3">
                <i class="fas fa-briefcase text-purple-600 text-sm"></i>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-900">Global Tech - Software License</p>
                <p class="text-xs text-gray-500">$28,500 • 50% probability</p>
              </div>
            </div>
            <div class="flex items-start">
              <div class="bg-yellow-100 p-2 rounded-full mr-3">
                <i class="fas fa-briefcase text-yellow-600 text-sm"></i>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-900">StartUp Co - Consulting</p>
                <p class="text-xs text-gray-500">$18,000 • 30% probability</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
          <div class="flex justify-between items-center">
            <h3 class="text-lg font-medium text-gray-900">Recent Activities</h3>
            <button data-action="openModal" data-target="addActivityModal" class="text-sm text-blue-600 hover:text-blue-800 font-medium">Add Activity</button>
          </div>
        </div>
        <div class="divide-y divide-gray-200">
          <div class="px-6 py-4 flex items-start">
            <div class="bg-blue-100 p-2 rounded-full mr-3">
              <i class="fas fa-phone-alt text-blue-600 text-sm"></i>
            </div>
            <div class="flex-1">
              <div class="flex justify-between">
                <p class="text-sm font-medium text-gray-900">Call with Sarah Miller</p>
                <p class="text-xs text-gray-500">Today, 10:30 AM</p>
              </div>
              <p class="text-sm text-gray-500 mt-1">Discussed proposal details and next steps for the cloud migration project.</p>
            </div>
          </div>
          <div class="px-6 py-4 flex items-start">
            <div class="bg-green-100 p-2 rounded-full mr-3">
              <i class="fas fa-envelope text-green-600 text-sm"></i>
            </div>
            <div class="flex-1">
              <div class="flex justify-between">
                <p class="text-sm font-medium text-gray-900">Email to David Wilson</p>
                <p class="text-xs text-gray-500">Yesterday, 3:45 PM</p>
              </div>
              <p class="text-sm text-gray-500 mt-1">Sent contract renewal proposal with updated pricing.</p>
            </div>
          </div>
          <div class="px-6 py-4 flex items-start">
            <div class="bg-purple-100 p-2 rounded-full mr-3">
              <i class="fas fa-calendar-check text-purple-600 text-sm"></i>
            </div>
            <div class="flex-1">
              <div class="flex justify-between">
                <p class="text-sm font-medium text-gray-900">Meeting with Acme Corp</p>
                <p class="text-xs text-gray-500">Yesterday, 11:00 AM</p>
              </div>
              <p class="text-sm text-gray-500 mt-1">Demo of our new product features. Positive feedback received.</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Hidden sections for other views -->
    <section data-view="contacts" class="hidden">
      <!-- Contacts view content would go here -->
    </section>

    <section data-view="opportunities" class="hidden">
      <!-- Opportunities view content would go here -->
    </section>

    <section data-view="reports" class="hidden">
      <!-- Reports view content would go here -->
    </section>
  </main>

  <!-- Modals -->
  <div id="newOpportunityModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">New Opportunity</h3>
      </div>
      <div class="p-6">
        <form id="opportunityForm">
          <div class="mb-4">
            <label for="opportunityName" class="block text-sm font-medium text-gray-700 mb-1">Opportunity Name</label>
            <input type="text" id="opportunityName" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" required>
          </div>
          <div class="mb-4">
            <label for="account" class="block text-sm font-medium text-gray-700 mb-1">Account</label>
            <select id="account" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" required>
              <option value="">Select an account</option>
              <option value="acme">Acme Corp</option>
              <option value="xyz">XYZ Inc</option>
              <option value="global">Global Tech</option>
            </select>
          </div>
          <div class="mb-4">
            <label for="amount" class="block text-sm font-medium text-gray-700 mb-1">Amount ($)</label>
            <input type="number" id="amount" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" required>
          </div>
          <div class="mb-4">
            <label for="stage" class="block text-sm font-medium text-gray-700 mb-1">Stage</label>
            <select id="stage" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" required>
              <option value="prospecting">Prospecting</option>
              <option value="qualification">Qualification</option>
              <option value="proposal">Proposal</option>
              <option value="negotiation">Negotiation</option>
              <option value="closed-won">Closed Won</option>
            </select>
          </div>
          <div class="mb-4">
            <label for="probability" class="block text-sm font-medium text-gray-700 mb-1">Probability (%)</label>
            <input type="number" id="probability" min="0" max="100" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" required>
          </div>
          <div class="flex justify-end space-x-3 mt-6">
            <button type="button" data-action="closeModal" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">Cancel</button>
            <button type="submit" class="px-4 py-2 bg-blue-600 border border-transparent rounded-md text-sm font-medium text-white hover:bg-blue-700">Save Opportunity</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <div id="quickContactModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Add New Contact</h3>
      </div>
      <div class="p-6">
        <form>
          <div class="grid grid-cols-2 gap-4 mb-4">
            <div>
              <label for="firstName" class="block text-sm font-medium text-gray-700 mb-1">First Name</label>
              <input type="text" id="firstName" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
            </div>
            <div>
              <label for="lastName" class="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
              <input type="text" id="lastName" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
            </div>
          </div>
          <div class="mb-4">
            <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
            <input type="email" id="email" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
          </div>
          <div class="mb-4">
            <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">Phone</label>
            <input type="tel" id="phone" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
          </div>
          <div class="mb-4">
            <label for="company" class="block text-sm font-medium text-gray-700 mb-1">Company</label>
            <input type="text" id="company" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
          </div>
          <div class="flex justify-end space-x-3 mt-6">
            <button type="button" data-action="closeModal" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">Cancel</button>
            <button type="submit" class="px-4 py-2 bg-blue-600 border border-transparent rounded-md text-sm font-medium text-white hover:bg-blue-700">Save Contact</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <div id="fullReportModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Opportunities Report</h3>
      </div>
      <div class="p-6">
        <div class="h-96 mb-6" data-action="renderChart" data-chart-type="detailedBar" data-chart-id="detailedOpportunitiesChart"></div>
        <div class="bg-gray-50 p-4 rounded-lg">
          <h4 class="text-md font-medium text-gray-900 mb-3">Key Insights</h4>
          <ul class="list-disc pl-5 space-y-2 text-sm text-gray-700">
            <li>Prospecting stage has increased by 15% compared to last quarter</li>
            <li>Negotiation stage opportunities have the highest average deal size at $42,000</li>
            <li>Closed Won rate is currently at 32%, up from 28% last quarter</li>
          </ul>
        </div>
        <div class="flex justify-end mt-6">
          <button data-action="closeModal" class="px-4 py-2 bg-blue-600 border border-transparent rounded-md text-sm font-medium text-white hover:bg-blue-700">Close</button>
        </div>
      </div>
    </div>
  </div>

  <div id="allOpportunitiesModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">All Opportunities</h3>
      </div>
      <div class="p-6">
        <div class="mb-4 flex justify-between items-center">
          <div class="relative rounded-md shadow-sm w-64">
            <input type="text" class="block w-full rounded-md border-gray-300 pl-4 pr-10 py-2 focus:border-blue-500 focus:ring-blue-500 sm:text-sm" placeholder="Search opportunities...">
            <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              <i class="fas fa-search text-gray-400"></i>
            </div>
          </div>
          <button data-action="openModal" data-target="newOpportunityModal" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200">
            <i class="fas fa-plus mr-2"></i> New Opportunity
          </button>
        </div>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Account</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stage</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Probability</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Acme Corp - Cloud Migration</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Acme Corp</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">$45,000</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Proposal</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">80%</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <button data-action="openModal" data-target="editOpportunityModal" class="text-blue-600 hover:text-blue-900 mr-3">Edit</button>
                  <button class="text-red-600 hover:text-red-900">Delete</button>
                </td>
              </tr>
              <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">XYZ Inc - Annual Contract</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">XYZ Inc</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">$32,000</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Negotiation</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">65%</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <button data-action="openModal" data-target="editOpportunityModal" class="text-blue-600 hover:text-blue-900 mr-3">Edit</button>
                  <button class="text-red-600 hover:text-red-900">Delete</button>
                </td>
              </tr>
              <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Global Tech - Software License</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Global Tech</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">$28,500</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Qualification</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">50%</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <button data-action="openModal" data-target="editOpportunityModal" class="text-blue-600 hover:text-blue-900 mr-3">Edit</button>
                  <button class="text-red-600 hover:text-red-900">Delete</button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="flex justify-between items-center mt-4">
          <div class="text-sm text-gray-500">Showing <span class="font-medium">1</span> to <span class="font-medium">3</span> of <span class="font-medium">24</span> opportunities</div>
          <div class="flex space-x-2">
            <button class="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">Previous</button>
            <button class="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">1</button>
            <button class="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">2</button>
            <button class="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">Next</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div id="addActivityModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Add Activity</h3>
      </div>
      <div class="p-6">
        <form>
          <div class="mb-4">
            <label for="activityType" class="block text-sm font-medium text-gray-700 mb-1">Activity Type</label>
            <select id="activityType" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
              <option value="call">Call</option>
              <option value="email">Email</option>
              <option value="meeting">Meeting</option>
              <option value="task">Task</option>
            </select>
          </div>
          <div class="mb-4">
            <label for="relatedTo" class="block text-sm font-medium text-gray-700 mb-1">Related To</label>
            <select id="relatedTo" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
              <option value="">Select an opportunity</option>
              <option value="acme">Acme Corp - Cloud Migration</option>
              <option value="xyz">XYZ Inc - Annual Contract</option>
              <option value="global">Global Tech - Software License</option>
            </select>
          </div>
          <div class="mb-4">
            <label for="subject" class="block text-sm font-medium text-gray-700 mb-1">Subject</label>
            <input type="text" id="subject" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
          </div>
          <div class="mb-4">
            <label for="dueDate" class="block text-sm font-medium text-gray-700 mb-1">Due Date</label>
            <input type="date" id="dueDate" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
          </div>
          <div class="mb-4">
            <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
            <textarea id="notes" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"></textarea>
          </div>
          <div class="flex justify-end space-x-3 mt-6">
            <button type="button" data-action="closeModal" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">Cancel</button>
            <button type="submit" class="px-4 py-2 bg-blue-600 border border-transparent rounded-md text-sm font-medium text-white hover:bg-blue-700">Save Activity</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <div id="editOpportunityModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Edit Opportunity</h3>
      </div>
      <div class="p-6">
        <form>
          <div class="mb-4">
            <label for="editOpportunityName" class="block text-sm font-medium text-gray-700 mb-1">Opportunity Name</label>
            <input type="text" id="editOpportunityName" value="Acme Corp - Cloud Migration" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
          </div>
          <div class="mb-4">
            <label for="editAccount" class="block text-sm font-medium text-gray-700 mb-1">Account</label>
            <select id="editAccount" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
              <option value="acme" selected>Acme Corp</option>
              <option value="xyz">XYZ Inc</option>
              <option value="global">Global Tech</option>
            </select>
          </div>
          <div class="mb-4">
            <label for="editAmount" class="block text-sm font-medium text-gray-700 mb-1">Amount ($)</label>
            <input type="number" id="editAmount" value="45000" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
          </div>
          <div class="mb-4">
            <label for="editStage" class="block text-sm font-medium text-gray-700 mb-1">Stage</label>
            <select id="editStage" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
              <option value="prospecting">Prospecting</option>
              <option value="qualification">Qualification</option>
              <option value="proposal" selected>Proposal</option>
              <option value="negotiation">Negotiation</option>
              <option value="closed-won">Closed Won</option>
            </select>
          </div>
          <div class="mb-4">
            <label for="editProbability" class="block text-sm font-medium text-gray-700 mb-1">Probability (%)</label>
            <input type="number" id="editProbability" value="80" min="0" max="100" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
          </div>
          <div class="flex justify-end space-x-3 mt-6">
            <button type="button" data-action="closeModal" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">Cancel</button>
            <button type="submit" class="px-4 py-2 bg-blue-600 border border-transparent rounded-md text-sm font-medium text-white hover:bg-blue-700">Save Changes</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>