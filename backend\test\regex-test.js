const sampleHTML = `
<div id="app">
  <header class="bg-blue-600">
    <nav class="nav">
      <h1>Dashboard</h1>
      <ul>
        <li><a href="#home">Home</a></li>
        <li><a href="#about">About</a></li>
      </ul>
    </nav>
  </header>
  <main>
    <div id="chart-container">Chart here</div>
  </main>
</div>
`;

console.log('🔍 Testing h1 regex patterns:\n');

// Test different regex patterns
const patterns = [
  { name: 'Current pattern', regex: /<h1[^>]*>.*?<\/h1>/gis },
  { name: 'Simple pattern', regex: /<h1>.*?<\/h1>/gi },
  { name: 'Flexible pattern', regex: /<h1[^>]*>[\s\S]*?<\/h1>/gi },
  { name: 'Non-greedy pattern', regex: /<h1[^>]*?>(.*?)<\/h1>/gi }
];

patterns.forEach(({ name, regex }) => {
  console.log(`Testing ${name}: ${regex}`);
  const match = sampleHTML.match(regex);
  console.log('Result:', match);
  console.log('');
});

console.log('🔍 Testing selector logic:\n');

const selector = 'h1';
const selectors = selector.split(',').map(s => s.trim());
console.log('Selectors array:', selectors);

for (const singleSelector of selectors) {
  console.log(`Processing selector: "${singleSelector}"`);
  console.log('Is simple tag?', /^[a-z]+$/i.test(singleSelector));
  
  if (/^[a-z]+$/i.test(singleSelector)) {
    const tag = singleSelector.toLowerCase();
    console.log(`Tag: ${tag}`);
    const tagRegex = new RegExp(`<${tag}[^>]*>.*?</${tag}>`, 'gis');
    console.log(`Regex: ${tagRegex}`);
    const match = sampleHTML.match(tagRegex);
    console.log('Match result:', match);
  }
}
