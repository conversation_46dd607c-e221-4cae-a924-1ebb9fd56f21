import { useEffect, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { FiPlus, FiEdit2, FiTrash2, <PERSON><PERSON><PERSON>, <PERSON>Loa<PERSON>, <PERSON>Zap } from 'react-icons/fi';
import styles from './MyPrototypesPage.module.css';
import { getPrototypes, deletePrototype } from '../services/prototypeService';

interface Prototype {
  id: number;
  title: string;
  description: string;
  html: string;
  css?: string;
  preview_image_url?: string;
  created_at: string;
  updated_at: string;
}

export function MyPrototypesPage() {
  const [prototypes, setPrototypes] = useState<Prototype[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleting, setDeleting] = useState<number | null>(null);
  const navigate = useNavigate();

  // Fetch prototypes on component mount
  useEffect(() => {
    fetchPrototypes();
  }, []);

  // Function to fetch prototypes
  const fetchPrototypes = async () => {
    try {
      setLoading(true);
      setError(null);

      // Check if user is logged in via cookie
      const isLoggedIn = document.cookie.includes('isLoggedIn=true');
      if (!isLoggedIn) {
        console.log('User not logged in, redirecting to login');
        // Redirect to home page which will show login button
        navigate('/');
        return;
      }

      const data = await getPrototypes();
      console.log('Fetched prototypes:', data);
      setPrototypes(data);
    } catch (err: any) {
      console.error('Error fetching prototypes:', err);

      // Check if this is an authentication error (401)
      if (err.status === 401 || err.message?.includes('Not authenticated')) {
        console.log('Authentication error, redirecting to login');
        // Clear any stale auth state
        localStorage.removeItem('authState');
        // Redirect to home page which will show login button
        navigate('/');
        return;
      }

      setError('Failed to load prototypes. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Function to handle prototype deletion
  const handleDelete = async (id: number, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent navigation

    if (!window.confirm('Are you sure you want to delete this prototype?')) {
      return;
    }

    try {
      setDeleting(id);
      await deletePrototype(id);
      // Remove the deleted prototype from the state
      setPrototypes(prototypes.filter(p => p.id !== id));
    } catch (err: any) {
      console.error('Error deleting prototype:', err);
      setError('Failed to delete prototype. Please try again.');
    } finally {
      setDeleting(null);
    }
  };

  // Function to handle prototype click (view)
  const handlePrototypeClick = (prototype: Prototype) => {
    navigate(`/prototype/${prototype.id}`);
  };

  // Function to handle edit click
  const handleEditClick = (id: number, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent navigation to view
    navigate(`/editor-v3-refactored/${id}`);
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString();
    } catch (e) {
      return 'Unknown date';
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h1>My Prototypes</h1>
        <div className="flex gap-3">
          <Link to="/editor-v3-refactored" className={styles.createButton} style={{ backgroundColor: '#ff6b35', borderColor: '#ff6b35' }}>
            <FiZap />
            Open Editor
          </Link>
          <Link to="/prompt" className={styles.createButton}>
            <FiPlus />
            Create New Prototype
          </Link>
        </div>
      </div>

      {error && <div className={styles.error}>{error}</div>}

      {loading ? (
        <div className={styles.loading}>
          <FiLoader className={styles.loadingIcon} />
          <span>Loading prototypes...</span>
        </div>
      ) : prototypes.length === 0 ? (
        <div className={styles.emptyState}>
          <div className={styles.emptyStateIcon}>
            <FiPlus />
          </div>
          <h2>No prototypes yet</h2>
          <p>Create your first prototype to get started</p>
          <Link to="/prompt" className={styles.createButton}>
            Create New Prototype
          </Link>
        </div>
      ) : (
        <div className={styles.simpleList}>
          {prototypes.map(prototype => (
            <div
              key={prototype.id}
              className={styles.listItem}
              onClick={() => handlePrototypeClick(prototype)}
            >
              <div className={styles.listContent}>
                <h3 className={styles.listTitle}>{prototype.title || 'Untitled Prototype'}</h3>
                <p className={styles.listDescription}>
                  {prototype.description
                    ? (prototype.description.length > 100
                        ? `${prototype.description.substring(0, 100)}...`
                        : prototype.description)
                    : 'No description'}
                </p>
                <div className={styles.listMeta}>
                  <span className={styles.date}>
                    Last updated: {formatDate(prototype.updated_at)}
                  </span>
                </div>
              </div>
              <div className={styles.listActions}>
                <button
                  className={styles.actionButton}
                  onClick={(e) => handleEditClick(prototype.id, e)}
                  title="Edit"
                  style={{ color: '#ff6b35' }}
                >
                  <FiEdit2 />
                </button>
                <button
                  className={styles.actionButton}
                  onClick={(e) => handleDelete(prototype.id, e)}
                  disabled={deleting === prototype.id}
                  title="Delete"
                >
                  {deleting === prototype.id ? (
                    <FiLoader className={styles.loadingIcon} />
                  ) : (
                    <FiTrash2 />
                  )}
                </button>
                <button
                  className={styles.actionButton}
                  onClick={(e) => {
                    e.stopPropagation();
                    window.open(`/prototype/${prototype.id}`, '_blank');
                  }}
                  title="Open in new tab"
                >
                  <FiEye />
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
